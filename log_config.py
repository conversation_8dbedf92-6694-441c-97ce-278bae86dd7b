import logging
import logging.handlers
import colorlog

import os

project_dir = os.path.dirname(os.path.abspath(__file__))
project_name = project_dir.split("/")[-1].split("\\")[-1]
# 获取logger对象
logger = logging.getLogger(project_name)

# 指定最低日志级别：（critical（严重） > error（错误） > warning（警告） > info（信息） > debug（调试））   
logger.setLevel(logging.DEBUG)

'''
控制台输出日志格式 ({} console_fmt)
%(log_color)s: colorlog特有的颜色控制符
%(asctime)s: 时间戳
%(filename)s line %(lineno)d: 文件名和行号，便于调试定位
[%(levelname)s]: 日志级别
%(message)s: 日志消息

文件格式 ({} file_fmt)
与控制台格式相同，但不包含颜色控制符
文件中不需要颜色，保持纯文本格式
'''

# 日志格化字符串
console_fmt = "%(log_color)s%(asctime)s %(filename)s line %(lineno)d: [%(levelname)s] %(message)s"
file_fmt = "%(asctime)s %(filename)s line %(lineno)d: [%(levelname)s] %(message)s"

'''
color_config = {
    "DEBUG": "cyan",
    "INFO": "green", 
    "WARNING": "yellow",
    "ERROR": "red",
    "CRITICAL": "bold_red",
}

颜色映射策略：
DEBUG: 青色 - 调试信息，不太重要
INFO: 绿色 - 正常信息，积极色调
WARNING: 黄色 - 警告信息，提醒注意
ERROR: 红色 - 错误信息，需要关注
CRITICAL: 粗体红色 - 严重错误，最高优先级
'''

# 控制台输出不同级别日志颜色设置
color_config = {
    "DEBUG": "cyan",
    "INFO": "green",
    "WARNING": "yellow",
    "ERROR": "red",
    "CRITICAL": "bold_red",
}

'''
双格式化器设计：
* 控制台格式化器：使用 colorlog 的 ColoredFormatter 类，设置日志颜色，支持彩色输出
* 文件格式化器：使用 logging.Formatter 类，设置日志格式，不支持彩色输出
'''
console_formatter = colorlog.ColoredFormatter(fmt=console_fmt, log_colors=color_config)
file_formatter = logging.Formatter(fmt=file_fmt)

# 输出到控制台
console_handler = logging.StreamHandler()
# 输出到文件
log_dir = os.path.join(project_dir, "log")
if not os.path.exists(log_dir):
    os.mkdir(log_dir)
file_name = os.path.join(log_dir, project_name + ".log")
file_handler = logging.handlers.TimedRotatingFileHandler(filename=file_name, when='D', interval=1, backupCount=7,
                                                         encoding="utf-8")

# 设置日志格式
console_handler.setFormatter(console_formatter)
file_handler.setFormatter(file_formatter)

# 处理器设置日志级别，不同处理器可各自设置级别，默认使用logger日志级别
console_handler.setLevel(logging.INFO)
file_handler.setLevel(logging.DEBUG)

# logger添加处理器
# 避免重复打印日志
if not logger.handlers:
    logger.addHandler(console_handler)
    logger.addHandler(file_handler)
