# 整个硬件兼容性检查系统的数据获取核心，负责从不同的蔚来平台获取车辆 ECU（电子控制单元）的软硬件版本信息
import json
import requests
from datetime import datetime
from typing import Optional

from log_config import logger

'''
json: 处理API响应的JSON数据
requests: HTTP请求库，用于调用REST API
datetime: 时间戳处理
typing.Optional: 类型注解，表示可能返回None
log_config.logger: 项目统一的日志记录器
'''
# GETEcuVersion类用于获取车辆的ECU版本信息，包括硬件版本、软件版本等
class GetEcuVersion:
    '''
    函数初始化：
    self.url: API的URL地址
    self.method: HTTP请求方法
    self.payload: 请求参数
    self.headers: 请求头
        cookie: 登录Cookie
        user-agent: 请求头中的User-Agent
        content-type: 请求头中的Content-Type

    Cookie 参数类型选择：Selenium的 driver.get_cookies() 方法返回的字典类型，需要转换为字符串类型
    self.url = " " #请求URL - 延迟地址
    self.method = " " #请求方法 - GET/POST
    self.payload = {} #请求参数 - 
    '''
    def __init__(self, cookie: dict):
        self.url = ""
        self.method = ""
        self.payload = {}
        self.headers = {
            "Cookie": "; ".join([str(x) + "=" + str(y) for x, y in cookie.items()]),
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) "
                          "Chrome/118.0.0.0 Safari/537.36",
            "Content-Type": "application/json"
        }
    '''
    query_by_vin - 函数功能：通过车辆VIN查询车辆信息
    输入：17位车辆标识码（VIN）
    输出：车辆信息字典，包含车辆ID、车辆型号、车辆VIN、车辆注册日期、车辆过户日期等信息
    API接口：https://request-proxy.nioint.com/proxy/tvas/v1/vehicles/query_by_vin
    用途：获取车辆型号，用于后续选择对应的兼容表格

    {
        "result_code": "success",
        "message": "ok", 
        "data": [
            {
            "vehicle_model": "Force",  // 车型代号
            "vin": "HJNAABGF2RB542058",
            // 其他车辆信息...
            }
        ]
    }
    '''
    def query_by_vin(self, vin: str) -> Optional[dict]:
        self.method = "GET"
        self.url = f"https://request-proxy.nioint.com/proxy/tvas/v1/vehicles/query_by_vin?vin={vin}"
        response = requests.request(self.method, self.url, headers=self.headers, data=self.payload)

        if response.ok and response.status_code == 200:
            response_dict = response.json()
            if response_dict.get("result_code") == "success" and response_dict.get("message") == "ok":
                data_list = response_dict.get("data")
                if data_list and len(data_list) > 0:
                    data_dict = data_list[0]
                    return data_dict
                else:
                    logger.error(f"No vehicle info found for {vin}, please check that the vin is correct!")
                    return None
            else:
                logger.error(response_dict)
                return None
        else:
            logger.error(response.text)
            return None
    '''
    query_ecu_version - 函数功能：通过车辆VIN查询车辆ECU版本信息
    输入：17位车辆标识码（VIN）
    输出：车辆ECU版本信息字典，包含车辆ID、采样时间、ECU版本信息字典
    API接口：https://request-proxy.nioint.com/proxy/tvas/v1/vehicles/query_ecu_version
    用途：获取车辆ECU版本信息，用于后续判断是否与目标整车软件包兼容

    {
        "sample_time": 1672531200,
        "vehicle_id": "12345",
        "ecu_version_map": {
            "ADC": {
            "ecu": "ADC",
            "software_pn": "SW123 V1.0",
            "hardware_pn": "HW456 A01"
            },
            "CDC": {
            "ecu": "CDC", 
            "software_pn": "SW789 V2.1",
            "hardware_pn": "HW012 B02"
            }
        }
    }
    '''
    def query_ecu_version(self, vin: str) -> Optional[dict]:
        self.method = "GET"
        self.url = f"https://request-proxy.nioint.com/proxy/tvas/v1/vehicles/query_ecu_version?vin={vin}"
        response = requests.request(self.method, self.url, headers=self.headers, data=self.payload)

        if response.ok and response.status_code == 200:
            response_dict = response.json()
            if response_dict.get("result_code") == "success" and response_dict.get("message") == "ok":
                data_dict = response_dict.get("data")
                if data_dict and data_dict.get("ecu_version_map"):
                    result_dict = {
                        "sample_time": data_dict.get("sample_time"),
                        "vehicle_id": data_dict.get("vehicle_id"),
                        "ecu_version_map": data_dict.get("ecu_version_map", {}),
                    }
                    return result_dict
                else:
                    logger.error(f"No ecu_version data for {vin}")
                    logger.warning(response_dict)
                    return None
            else:
                logger.error(response_dict)
                return None
        else:
            logger.error(response.text)
            return None

# GetEcuVersionByFota类用于获取车辆的ECU版本信息，通过FOTA平台获取
class GetEcuVersionByFota:
    def __init__(self, cookie: str, base_url="https://fota-web-onvo-stg.nioint.com"):
        self.base_url = base_url
        self.url = ""
        self.method = ""
        self.payload = {}
        self.headers = {
            "Cookie": cookie,
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) "
                          "Chrome/118.0.0.0 Safari/537.36",
            "Content-Type": "application/json"
        }
    '''
    query_vehicle_info - 函数功能：通过车辆VIN查询车辆信息
    输入：17位车辆标识码（VIN）
    输出：车辆信息字典，包含车辆ID、车辆型号、车辆VIN、车辆注册日期、车辆过户日期等信息
    API接口：https://fota-web-onvo-stg.nioint.com/app/failuremgr/apiself/delivery/vehicles/query
    用途：获取车辆ID，用于后续查询车辆ECU版本信息

    请求体结构:
    {
        "filterRules": [
            {
            "field": "vin",
            "op": "contains", 
            "value": "HJNAABGF2RB542058"
            }
        ],
        "page": 1,
        "pageSize": 10
    }
    '''
    def query_vehicle_info(self, vin: str) -> Optional[dict]:
        self.method = "POST"
        self.url = self.base_url + "/app/failuremgr/apiself/delivery/vehicles/query"
        self.payload = json.dumps({
            "filterRules": [{"field": "vin", "op": "contains", "value": vin}],
            "page": 1, "pageSize": 10
        })
        response = requests.request(self.method, self.url, headers=self.headers, data=self.payload)

        if response.ok and response.status_code == 200:
            response_dict = response.json()
            if response_dict.get("retcode") == 0 and response_dict.get("msg") == "successfully":
                data_list = response_dict.get("data")
                if data_list and len(data_list) > 0:
                    data_dict = data_list[0]
                    return data_dict

        logger.error(response.text)
        return None

    '''
    query_ecu_version_by_fota - 函数功能：通过FOTA平台查询车辆ECU版本信息
    输入：车辆ID（vid）
    输出：车辆ECU版本信息字典，包含ECU名称、软件版本、硬件版本等信息
    API接口：https://fota-web-onvo-stg.nioint.com/app/failuremgr/apiself/delivery/dids/query
    用途：获取车辆ECU版本信息，用于后续判断是否与目标整车软件包兼容
    '''
    def query_ecu_version_by_fota(self, vid: str) -> Optional[dict]:
        logger.debug(f"Querying ecu version by fota for {vid}")
        if vid:
            self.method = "POST"
            self.url = self.base_url + "/app/failuremgr/apiself/delivery/dids/query"
            self.payload = json.dumps({"vid": vid})
            response = requests.request(self.method, self.url, headers=self.headers, data=self.payload)

            ecu_version_map = {}
            if response.ok and response.status_code == 200:
                response_dict = response.json()
                if response_dict.get("retcode") == 0 and response_dict.get("msg").lower() == "successfully":
                    iso_time_str = response_dict.get("upload_at")
                    data_list = response_dict.get("data", [])
                    if data_list and len(data_list) > 0:
                        for data_dict in data_list:
                            did_key = data_dict.get("name")
                            ecu_name = data_dict.get("group")
                            did_value = data_dict.get("value", "")
                            if ecu_name is None:
                                continue
                            if ecu_name not in ecu_version_map.keys():
                                ecu_version_map[ecu_name] = {"ecu": ecu_name}
                            if did_key == "F110":
                                ecu_version_map[ecu_name]["hardware_pn"] = did_value
                            elif did_key == "F118":
                                ecu_version_map[ecu_name]["software_pn"] = did_value

                        ecu_did = {
                            "vehicle_id": vid,
                            "sample_time": datetime.fromisoformat(iso_time_str).timestamp(),
                            "ecu_version_map": ecu_version_map
                        }
                        return ecu_did

            logger.error(response.text)
            return None
        return None
