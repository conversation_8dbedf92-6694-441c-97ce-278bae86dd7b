import json
import requests
from datetime import datetime
from typing import Optional

from log_config import logger


class GetEcuVersion:
    def __init__(self, cookie: dict):
        self.url = ""
        self.method = ""
        self.payload = {}
        self.headers = {
            "Cookie": "; ".join([str(x) + "=" + str(y) for x, y in cookie.items()]),
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) "
                          "Chrome/118.0.0.0 Safari/537.36",
            "Content-Type": "application/json"
        }

    def query_by_vin(self, vin: str) -> Optional[dict]:
        self.method = "GET"
        self.url = f"https://request-proxy.nioint.com/proxy/tvas/v1/vehicles/query_by_vin?vin={vin}"
        response = requests.request(self.method, self.url, headers=self.headers, data=self.payload)

        if response.ok and response.status_code == 200:
            response_dict = response.json()
            if response_dict.get("result_code") == "success" and response_dict.get("message") == "ok":
                data_list = response_dict.get("data")
                if data_list and len(data_list) > 0:
                    data_dict = data_list[0]
                    return data_dict
                else:
                    logger.error(f"No vehicle info found for {vin}, please check that the vin is correct!")
                    return None
            else:
                logger.error(response_dict)
                return None
        else:
            logger.error(response.text)
            return None

    def query_ecu_version(self, vin: str) -> Optional[dict]:
        self.method = "GET"
        self.url = f"https://request-proxy.nioint.com/proxy/tvas/v1/vehicles/query_ecu_version?vin={vin}"
        response = requests.request(self.method, self.url, headers=self.headers, data=self.payload)

        if response.ok and response.status_code == 200:
            response_dict = response.json()
            if response_dict.get("result_code") == "success" and response_dict.get("message") == "ok":
                data_dict = response_dict.get("data")
                if data_dict and data_dict.get("ecu_version_map"):
                    result_dict = {
                        "sample_time": data_dict.get("sample_time"),
                        "vehicle_id": data_dict.get("vehicle_id"),
                        "ecu_version_map": data_dict.get("ecu_version_map", {}),
                    }
                    return result_dict
                else:
                    logger.error(f"No ecu_version data for {vin}")
                    logger.warning(response_dict)
                    return None
            else:
                logger.error(response_dict)
                return None
        else:
            logger.error(response.text)
            return None


class GetEcuVersionByFota:
    def __init__(self, cookie: str, base_url="https://fota-web-onvo-stg.nioint.com"):
        self.base_url = base_url
        self.url = ""
        self.method = ""
        self.payload = {}
        self.headers = {
            "Cookie": cookie,
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) "
                          "Chrome/118.0.0.0 Safari/537.36",
            "Content-Type": "application/json"
        }

    def query_vehicle_info(self, vin: str) -> Optional[dict]:
        self.method = "POST"
        self.url = self.base_url + "/app/failuremgr/apiself/delivery/vehicles/query"
        self.payload = json.dumps({
            "filterRules": [{"field": "vin", "op": "contains", "value": vin}],
            "page": 1, "pageSize": 10
        })
        response = requests.request(self.method, self.url, headers=self.headers, data=self.payload)

        if response.ok and response.status_code == 200:
            response_dict = response.json()
            if response_dict.get("retcode") == 0 and response_dict.get("msg") == "successfully":
                data_list = response_dict.get("data")
                if data_list and len(data_list) > 0:
                    data_dict = data_list[0]
                    return data_dict

        logger.error(response.text)
        return None

    def query_ecu_version_by_fota(self, vid: str) -> Optional[dict]:
        logger.debug(f"Querying ecu version by fota for {vid}")
        if vid:
            self.method = "POST"
            self.url = self.base_url + "/app/failuremgr/apiself/delivery/dids/query"
            self.payload = json.dumps({"vid": vid})
            response = requests.request(self.method, self.url, headers=self.headers, data=self.payload)

            ecu_version_map = {}
            if response.ok and response.status_code == 200:
                response_dict = response.json()
                if response_dict.get("retcode") == 0 and response_dict.get("msg").lower() == "successfully":
                    iso_time_str = response_dict.get("upload_at")
                    data_list = response_dict.get("data", [])
                    if data_list and len(data_list) > 0:
                        for data_dict in data_list:
                            did_key = data_dict.get("name")
                            ecu_name = data_dict.get("group")
                            did_value = data_dict.get("value", "")
                            if ecu_name is None:
                                continue
                            if ecu_name not in ecu_version_map.keys():
                                ecu_version_map[ecu_name] = {"ecu": ecu_name}
                            if did_key == "F110":
                                ecu_version_map[ecu_name]["hardware_pn"] = did_value
                            elif did_key == "F118":
                                ecu_version_map[ecu_name]["software_pn"] = did_value

                        ecu_did = {
                            "vehicle_id": vid,
                            "sample_time": datetime.fromisoformat(iso_time_str).timestamp(),
                            "ecu_version_map": ecu_version_map
                        }
                        return ecu_did

            logger.error(response.text)
            return None
        return None
