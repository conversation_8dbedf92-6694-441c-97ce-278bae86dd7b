import os
# __file__ 是当前模块的名称
print(__file__)
abs_path = os.path.abspath(__file__)
# abs_path 是当前模块的绝对路径
print(abs_path)
'''
test.py - __file__
/home/<USER>/Downloads/hardware_compatibility/test.py - abs_path
'''
# os.path.dirname(__file__) - 获取目录部分
# /home/<USER>/Downloads/hardware_compatibility
project_dir = os.path.dirname(abs_path)
print(project_dir)

# hardware_compatibility
project_name = os.path.basename(project_dir)
print(project_name)

# hardware_compatibility
project_name = project_dir.split('/')[-1].split('\\')[-1]
print(project_name)

'''
第一次split("/") - 按Unix/Linux路径分隔符分割
# Linux/macOS 路径：/home/<USER>/Downloads/hardware_compatibility
parts = project_dir.split("/")
# 结果：['', 'home', 'nio', 'Downloads', 'hardware_compatibility']

# Windows 路径：C:\Users\<USER>\Downloads\hardware_compatibility  
parts = project_dir.split("/")
# 结果：['C:\\Users\\<USER>\\Downloads\\hardware_compatibility']  # 没有分割

取最后一个元素 [-1]
# Linux/macOS 情况：
last_part = parts[-1]  # 'hardware_compatibility'

# Windows 情况：
last_part = parts[-1]  # 'C:\\Users\\<USER>\\Downloads\\hardware_compatibility'
'''